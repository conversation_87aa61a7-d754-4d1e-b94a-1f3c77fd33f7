import type { Node } from '@swc/core';
import type { CalculationOptions, FunctionResult, PositionStrategy, PositionStrategyEntry, TokenSearchResult, TokenSearchRange } from './types';
import type { AsyncRuleEngine } from '../engine/types';
import { BaseVisitor } from './base-visitor';
import { DetailCollector } from './detail-collector';
import { PositionConverter } from '../utils/position-converter';
import { RuleRegistry } from './rule-registry';
import { RuleCategory } from './types';

// 初始化默认规则到RuleRegistry
RuleRegistry.register('if-statement', 'if 语句', RuleCategory.CONTROL_FLOW, 1);
RuleRegistry.register('while-statement', 'while 循环', RuleCategory.CONTROL_FLOW, 1);
RuleRegistry.register('do-while-statement', 'do-while 循环', RuleCategory.CONTROL_FLOW, 1);
RuleRegistry.register('for-statement', 'for 循环', RuleCategory.CONTROL_FLOW, 1);
RuleRegistry.register('switch-statement', 'switch 语句', RuleCategory.CONTROL_FLOW, 1);
RuleRegistry.register('try-statement', 'try 语句', RuleCategory.EXCEPTION_HANDLING, 0);
RuleRegistry.register('catch-clause', 'catch 异常处理', RuleCategory.EXCEPTION_HANDLING, 1);
RuleRegistry.register('conditional-expression', '三元运算符', RuleCategory.CONTROL_FLOW, 1);
RuleRegistry.register('logical-operator', '逻辑运算符', RuleCategory.LOGICAL_OPERATOR, 1);
RuleRegistry.register('recursive-call', '递归调用', RuleCategory.RECURSION, 1);
RuleRegistry.register('logical-operator-mixing', '逻辑运算符混用惩罚', RuleCategory.LOGICAL_OPERATOR, 1);

/**
 * SWC 节点的 span 类型定义
 */
interface SwcSpan {
  start: number;
  end: number;
  ctxt: number;
}

/**
 * 具有 span 属性的节点类型
 */
interface NodeWithSpan extends Node {
  span: SwcSpan;
}

/**
 * ComplexityVisitor - 认知复杂度计算访问者
 * 
 * 基于访问者模式实现的复杂度计算器，专门为单个函数计算认知复杂度。
 * 实现结构化的 span 修正策略，优先使用父节点信息进行位置回退。
 * 支持异步规则引擎和传统规则注册表的双模式运行。
 * 
 * L1 层策略映射表增强：
 * - 实现基于节点类型的智能位置定位策略
 * - 支持多级回退机制（节点策略 → 父节点 → 默认位置）
 * - 提供 Token 查找系统支持精确关键字定位
 * - 运行时可扩展的策略映射表
 * 
 * 核心功能：
 * - 遍历单个函数的 AST 节点并计算认知复杂度
 * - 维护嵌套层级，支持嵌套结构的复杂度计算
 * - 实现基于父节点的结构化 span 修正
 * - 与 DetailCollector 集成，收集详细的计算步骤
 * - 存储多个函数的分析结果
 * - 支持 AsyncRuleEngine 和 RuleRegistry 的双模式
 * 
 * 设计原则：
 * - 遵循访问者模式，分离 AST 结构遍历和复杂度计算逻辑
 * - 实现"结构性修正层"，基于 AST 父节点信息进行精确的 span 修正
 * - 支持详细的复杂度计算日志记录和调试
 * - 错误恢复机制，确保部分节点失败不影响整体计算
 */
export class ComplexityVisitor extends BaseVisitor {
  // =============================================================================
  // L1 层策略映射表 - 节点位置定位策略系统
  // =============================================================================

  /**
   * 节点位置策略映射表
   * 为不同类型的 AST 节点提供专门的位置定位策略
   * 支持运行时扩展和动态配置
   */
  private static NODE_POSITION_STRATEGIES: Map<string, PositionStrategyEntry> = new Map([
    // 控制流语句策略
    ['IfStatement', {
      nodeType: 'IfStatement',
      strategy: ComplexityVisitor.createIfStatementStrategy(),
      priority: 1,
      fallbackStrategy: ComplexityVisitor.createControlFlowFallbackStrategy('if')
    }],
    ['WhileStatement', {
      nodeType: 'WhileStatement', 
      strategy: ComplexityVisitor.createWhileStatementStrategy(),
      priority: 1,
      fallbackStrategy: ComplexityVisitor.createControlFlowFallbackStrategy('while')
    }],
    ['ForStatement', {
      nodeType: 'ForStatement',
      strategy: ComplexityVisitor.createForStatementStrategy(), 
      priority: 1,
      fallbackStrategy: ComplexityVisitor.createControlFlowFallbackStrategy('for')
    }]
  ]);

  /**
   * 注册新的节点位置策略
   * 支持运行时扩展策略映射表
   * @param entry 策略映射表条目
   */
  public static registerPositionStrategy(entry: PositionStrategyEntry): void {
    ComplexityVisitor.NODE_POSITION_STRATEGIES.set(entry.nodeType, entry);
  }

  /**
   * 获取指定节点类型的位置策略
   * @param nodeType 节点类型
   * @returns 策略映射条目，如果不存在则返回 undefined
   */
  public static getPositionStrategy(nodeType: string): PositionStrategyEntry | undefined {
    return ComplexityVisitor.NODE_POSITION_STRATEGIES.get(nodeType);
  }

  // =============================================================================
  // 策略函数工厂方法 - 创建具体的位置定位策略
  // =============================================================================

  /**
   * 创建 IfStatement 节点的位置策略
   * 优先查找 'if' 关键字位置
   */
  private static createIfStatementStrategy(): PositionStrategy {
    return (node: Node): number | null => {
      // 尝试通过 Token 查找 'if' 关键字
      const ifPosition = ComplexityVisitor.findKeywordInNode(node, 'if');
      if (ifPosition !== null) {
        return ifPosition;
      }
      
      // 回退到节点自身的 span
      const nodeWithSpan = node as any;
      if (nodeWithSpan.span && typeof nodeWithSpan.span.start === 'number') {
        return nodeWithSpan.span.start;
      }
      
      return null;
    };
  }

  /**
   * 创建 WhileStatement 节点的位置策略
   * 优先查找 'while' 关键字位置
   */
  private static createWhileStatementStrategy(): PositionStrategy {
    return (node: Node): number | null => {
      // 尝试通过 Token 查找 'while' 关键字
      const whilePosition = ComplexityVisitor.findKeywordInNode(node, 'while');
      if (whilePosition !== null) {
        return whilePosition;
      }
      
      // 回退到节点自身的 span
      const nodeWithSpan = node as any;
      if (nodeWithSpan.span && typeof nodeWithSpan.span.start === 'number') {
        return nodeWithSpan.span.start;
      }
      
      return null;
    };
  }

  /**
   * 创建 ForStatement 节点的位置策略
   * 优先查找 'for' 关键字位置
   */
  private static createForStatementStrategy(): PositionStrategy {
    return (node: Node): number | null => {
      // 尝试通过 Token 查找 'for' 关键字
      const forPosition = ComplexityVisitor.findKeywordInNode(node, 'for');
      if (forPosition !== null) {
        return forPosition;
      }
      
      // 回退到节点自身的 span
      const nodeWithSpan = node as any;
      if (nodeWithSpan.span && typeof nodeWithSpan.span.start === 'number') {
        return nodeWithSpan.span.start;
      }
      
      return null;
    };
  }

  /**
   * 创建控制流语句的通用回退策略
   * 当主策略失败时使用的回退逻辑
   * @param _keyword 控制流关键字（未使用，保留用于未来扩展）
   */
  private static createControlFlowFallbackStrategy(_keyword: string): PositionStrategy {
    return (_node: Node): number | null => {
      // 简单的字符串搜索作为最后的回退
      // 在实际实现中，这里可以访问 sourceCode 进行搜索
      // 由于这是静态方法，暂时返回 null，将由实例方法处理
      return null;
    };
  }

  // =============================================================================
  // Token 查找系统 - 基于 SWC Token 流的精确关键字定位
  // =============================================================================

  /**
   * 在节点中查找指定关键字的位置
   * 这是一个简化版本，实际实现会使用 SWC Token API
   * @param _node AST 节点（未使用，保留用于未来扩展）
   * @param _keyword 要查找的关键字（未使用，保留用于未来扩展）
   * @returns 关键字位置，如果未找到则返回 null
   */
  private static findKeywordInNode(_node: Node, _keyword: string): number | null {
    // 简化实现：直接返回 null，由实例方法中的复杂逻辑处理
    // 在实际实现中，这里会使用 SWC Token API 进行精确查找
    return null;
  }
  /**
   * 源代码内容，用于位置转换和 span 修正
   */
  private readonly sourceCode: string;

  /**
   * 详细信息收集器，用于记录复杂度计算步骤
   */
  private readonly detailCollector?: DetailCollector;

  /**
   * 计算选项，用于配置复杂度计算行为
   */
  private readonly options: CalculationOptions;

  /**
   * 异步规则引擎实例（优先使用）
   */
  private readonly asyncRuleEngine?: AsyncRuleEngine;

  /**
   * 函数分析结果存储
   */
  private results: FunctionResult[] = [];

  /**
   * 当前累计复杂度
   */
  private totalComplexity: number = 0;

  /**
   * 当前嵌套层级
   * 用于计算嵌套结构（如 if、for、while 等）的复杂度增量
   */
  private nestingLevel: number = 0;

  /**
   * 当前正在分析的函数名称
   */
  private currentFunctionName: string = '';

  /**
   * 当前函数的起始位置
   */
  private currentFunctionLocation: { line: number; column: number } = { line: 0, column: 0 };

  /**
   * 已处理的混用表达式节点集合
   * 用于确保同一表达式树不重复应用混用惩罚
   */
  private processedMixingNodes: Set<any> = new Set();

  /**
   * 构造函数
   * @param sourceCode 源代码内容，用于位置转换
   * @param detailCollector 可选的详细信息收集器
   * @param options 可选的计算选项
   * @param asyncRuleEngine 可选的异步规则引擎（如果提供，将优先使用）
   */
  constructor(
    sourceCode: string = '', 
    detailCollector?: DetailCollector, 
    options: CalculationOptions = {},
    asyncRuleEngine?: AsyncRuleEngine
  ) {
    super();
    this.sourceCode = sourceCode;
    this.detailCollector = detailCollector;
    this.options = options;
    this.asyncRuleEngine = asyncRuleEngine;
  }

  /**
   * 获取所有函数的分析结果
   * @returns 函数分析结果数组
   */
  public getResults(): FunctionResult[] {
    return [...this.results]; // 返回副本确保不可变
  }

  /**
   * 分析单个函数的入口方法
   * 初始化函数分析上下文，遍历函数体，收集结果
   * @param node 函数节点（FunctionDeclaration、ArrowFunctionExpression等）
   */
  public visitFunction(node: any): void {
    try {
      // 重置状态
      this.resetForNewFunction();
      
      // 提取函数信息
      this.currentFunctionName = this.extractFunctionName(node);
      this.currentFunctionLocation = this.getNodeLocation(node);
      
      // 开始详细模式跟踪（如果启用）
      if (this.detailCollector) {
        this.detailCollector.startFunction(
          this.currentFunctionName, 
          this.currentFunctionLocation.line, 
          this.currentFunctionLocation.column
        );
      }
      
      // 遍历函数体
      const functionBody = this.getFunctionBody(node);
      if (functionBody) {
        this.visit(functionBody);
      }
      
      // 结束详细模式跟踪
      let functionDetail = null;
      if (this.detailCollector) {
        functionDetail = this.detailCollector.endFunction();
      }
      
      // 收集结果
      const result: FunctionResult = {
        name: this.currentFunctionName,
        complexity: this.totalComplexity,
        line: this.currentFunctionLocation.line,
        column: this.currentFunctionLocation.column,
        filePath: '', // 将由调用者设置
        details: functionDetail?.details
      };
      
      this.results.push(result);
      
    } catch (error) {
      // 函数级错误处理
      this.handleFunctionAnalysisError(node, error as Error);
    }
  }

  /**
   * 重置状态以分析新函数
   */
  private resetForNewFunction(): void {
    this.totalComplexity = 0;
    this.nestingLevel = 0;
    this.processedMixingNodes.clear();
    this.currentFunctionName = '';
    this.currentFunctionLocation = { line: 0, column: 0 };
    this.reset(); // 调用父类的重置方法清空父节点栈
  }

  /**
   * 提取函数名称
   * @param node 函数节点
   * @returns 函数名称
   */
  private extractFunctionName(node: any): string {
    // 函数声明
    if (node.type === 'FunctionDeclaration' && node.identifier) {
      return node.identifier.value || node.identifier.name || '<anonymous>';
    }
    
    // 方法定义
    if (node.type === 'MethodDefinition' && node.key) {
      return node.key.value || node.key.name || '<method>';
    }
    
    // 类方法
    if (node.type === 'ClassMethod' && node.key) {
      return node.key.value || node.key.name || '<method>';
    }
    
    // 变量声明中的函数表达式
    if (node.type === 'VariableDeclarator' && node.id) {
      return node.id.value || node.id.name || '<anonymous>';
    }
    
    // 属性中的函数
    if (node.type === 'Property' && node.key) {
      return node.key.value || node.key.name || '<property>';
    }
    
    // 箭头函数等其他情况
    return '<anonymous>';
  }

  /**
   * 获取函数体节点
   * @param node 函数节点
   * @returns 函数体节点
   */
  private getFunctionBody(node: any): any {
    // 函数声明
    if (node.type === 'FunctionDeclaration' && node.body) {
      return node.body;
    }
    
    // 箭头函数
    if (node.type === 'ArrowFunctionExpression') {
      return node.body;
    }
    
    // 函数表达式
    if (node.type === 'FunctionExpression' && node.body) {
      return node.body;
    }
    
    // 方法定义
    if (node.type === 'MethodDefinition' && node.value && node.value.body) {
      return node.value.body;
    }
    
    // 类方法
    if (node.type === 'ClassMethod' && node.function && node.function.body) {
      return node.function.body;
    }
    
    // 变量声明中的函数
    if (node.type === 'VariableDeclarator' && node.init) {
      return this.getFunctionBody(node.init);
    }
    
    // 属性中的函数
    if (node.type === 'Property' && node.value) {
      return this.getFunctionBody(node.value);
    }
    
    return null;
  }

  /**
   * 获取节点位置信息
   * @param node 节点
   * @returns 位置信息
   */
  private getNodeLocation(node: any): { line: number; column: number } {
    try {
      // 尝试方法1：使用有效的span信息
      if (this.isValidSpan(node)) {
        const position = PositionConverter.spanToPosition(this.sourceCode, (node as NodeWithSpan).span.start);
        // 验证位置信息的合理性
        if (position.line >= 1 && position.column >= 0) {
          return { line: position.line, column: position.column };
        }
      }
      
      // 尝试方法2：检查节点是否有直接的位置属性
      if (node.loc) {
        const loc = node.loc;
        if (loc.start && typeof loc.start.line === 'number' && typeof loc.start.column === 'number') {
          if (loc.start.line >= 1 && loc.start.column >= 0) {
            return { line: loc.start.line, column: loc.start.column };
          }
        }
      }
      
      // 尝试方法3：检查是否有行列属性
      if (typeof node.line === 'number' && typeof node.column === 'number') {
        if (node.line >= 1 && node.column >= 0) {
          return { line: node.line, column: node.column };
        }
      }
      
      // 尝试方法4：如果是函数节点，尝试从标识符获取位置
      if (node.type === 'FunctionDeclaration' || node.type === 'FunctionExpression' || 
          node.type === 'ArrowFunctionExpression' || node.type === 'MethodDefinition') {
        const identifier = node.key || node.id;
        if (identifier && this.isValidSpan(identifier)) {
          const position = PositionConverter.spanToPosition(this.sourceCode, (identifier as NodeWithSpan).span.start);
          if (position.line >= 1 && position.column >= 0) {
            return { line: position.line, column: position.column };
          }
        }
      }
      
    } catch (error) {
      // 位置转换失败时的调试信息
      if (this.options?.enableDebugLog) {
        console.debug('Position extraction failed:', {
          nodeType: node.type,
          hasSpan: !!node.span,
          hasLoc: !!node.loc,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }
    
    // 所有方法都失败，返回默认值，但在调试模式下记录问题
    if (this.options?.enableDebugLog) {
      console.warn(`Unable to extract valid position for node type: ${node.type}, falling back to (1,0)`);
    }
    
    return { line: 1, column: 0 };
  }

  /**
   * 处理函数分析错误
   * @param node 函数节点
   * @param error 错误信息
   */
  private handleFunctionAnalysisError(node: any, error: Error): void {
    const functionName = this.extractFunctionName(node);
    const location = this.getNodeLocation(node);
    
    console.warn(`Error analyzing function ${functionName}:`, error.message);
    
    // 添加错误结果
    const errorResult: FunctionResult = {
      name: functionName,
      complexity: 0,
      line: location.line,
      column: location.column,
      filePath: '',
      severity: 'Critical'  
    };
    
    this.results.push(errorResult);
    
    // 记录到详细日志
    if (this.detailCollector) {
      try {
        this.detailCollector.addErrorStep(
          {
            line: location.line,
            column: location.column,
            increment: 0,
            ruleId: 'function-analysis-error',
            description: `函数分析失败: ${functionName}`,
            context: error.message
          },
          `函数 ${functionName} 分析过程中发生错误: ${error.message}`
        );
      } catch (detailError) {
        console.error(`Failed to record function analysis error for ${functionName}:`, detailError);
      }
    }
  }

  /**
   * 获取当前累计的总复杂度（向后兼容方法）
   * @returns 总复杂度值
   */
  public getTotalComplexity(): number {
    return this.totalComplexity;
  }

  /**
   * 获取当前嵌套层级
   * @returns 当前嵌套层级
   */
  public getCurrentNestingLevel(): number {
    return this.nestingLevel;
  }

  /**
   * 重置访问者状态
   * 清除复杂度和嵌套层级，用于复用访问者实例
   */
  public resetComplexity(): void {
    this.totalComplexity = 0;
    this.nestingLevel = 0;
    this.processedMixingNodes.clear();
    this.reset(); // 调用父类的重置方法清空父节点栈
  }

  /**
   * 重写基类的visit方法以正确处理嵌套层级
   * 同时自动检测函数节点并进行函数级分析
   * @param node 要访问的节点
   * @returns 访问后的节点
   */
  public override visit<T extends Node>(node: T): T {
    // 如果是顶层调用且为模块或程序节点，查找所有函数并分析
    if (this.parentStack.length === 0 && (node.type === 'Module' || node.type === 'Program')) {
      return this.visitModuleAndAnalyzeFunctions(node);
    }
    
    // 推入当前节点作为子节点的父节点
    this.parentStack.push(node);
    
    try {
      // 检查是否是嵌套结构节点，如果是先计算复杂度
      const shouldEnterNesting = this.shouldEnterNesting(node);
      
      // 计算当前节点的复杂度
      this.calculateNodeComplexity(node);
      
      // 如果需要进入嵌套，在访问子节点前增加嵌套层级
      if (shouldEnterNesting) {
        this.enterNesting();
      }
      
      try {
        // 访问子节点
        this.visitChildren(node);
      } finally {
        // 如果进入了嵌套，在访问完子节点后退出嵌套
        if (shouldEnterNesting) {
          this.exitNesting();
        }
      }
      
      return node;
    } catch (error) {
      // 错误恢复：记录错误但继续处理其他节点
      this.handleVisitError(node, error as Error);
      return node;
    } finally {
      // 确保在任何情况下都会弹出栈
      this.parentStack.pop();
    }
  }

  /**
   * 访问模块节点并自动分析所有函数
   * @param node 模块或程序节点
   * @returns 处理后的节点
   */
  private visitModuleAndAnalyzeFunctions<T extends Node>(node: T): T {
    // 找到所有函数节点
    const functions = this.findFunctionNodes(node);
    
    // 对每个函数节点调用 visitFunction
    for (const functionNode of functions) {
      this.visitFunction(functionNode);
    }
    
    return node;
  }

  /**
   * 在 AST 中查找所有函数节点
   * @param node 根节点
   * @returns 函数节点数组
   */
  private findFunctionNodes(node: any): any[] {
    const functions: any[] = [];
    
    const traverse = (currentNode: any) => {
      if (!currentNode) return;
      
      // 检查是否是函数节点
      if (this.isFunctionNode(currentNode)) {
        functions.push(currentNode);
        return; // 不深入函数内部查找嵌套函数（避免重复计算）
      }
      
      // 递归遍历子节点
      if (currentNode.body && Array.isArray(currentNode.body)) {
        for (const child of currentNode.body) {
          traverse(child);
        }
      } else if (currentNode.body && typeof currentNode.body === 'object') {
        traverse(currentNode.body);
      }
      
      // 处理其他可能包含函数的节点类型
      if (currentNode.declarations && Array.isArray(currentNode.declarations)) {
        for (const decl of currentNode.declarations) {
          traverse(decl);
        }
      }
      
      if (currentNode.init) {
        traverse(currentNode.init);
      }
      
      if (currentNode.consequent) {
        traverse(currentNode.consequent);
      }
      
      if (currentNode.alternate) {
        traverse(currentNode.alternate);
      }
    };
    
    traverse(node);
    return functions;
  }

  /**
   * 判断节点是否是函数节点
   * @param node 节点
   * @returns 是否是函数节点
   */
  private isFunctionNode(node: any): boolean {
    return node && (
      node.type === 'FunctionDeclaration' ||
      node.type === 'FunctionExpression' ||
      node.type === 'ArrowFunctionExpression' ||
      node.type === 'MethodDefinition' ||
      node.type === 'ClassMethod'
    );
  }

  /**
   * 判断节点是否应该影响嵌套层级
   * @param node 节点
   * @returns 是否应该影响嵌套层级
   */
  private shouldEnterNesting(node: Node): boolean {
    switch (node.type) {
      case 'IfStatement':
      case 'WhileStatement':
      case 'DoWhileStatement':
      case 'ForStatement':
      case 'ForInStatement':
      case 'ForOfStatement':
      case 'SwitchStatement':
      case 'CatchClause':
        return true;
      default:
        return false;
    }
  }

  /**
   * 访问节点的核心实现（已弃用，由visit方法直接处理）
   * @param node 要访问的节点
   * @returns 访问后的节点
   */
  protected visitNode(node: Node): Node {
    // 这个方法不再使用，逻辑已迁移到visit方法中
    return node;
  }

  /**
   * 根据节点类型计算复杂度
   * 使用规则引擎委托进行复杂度评估
   * @param node AST 节点
   */
  private calculateNodeComplexity(node: Node): void {
    // 委托给规则引擎获取复杂度
    const { complexity, ruleId, description } = this.evaluateNodeWithRules(node);
    
    if (complexity > 0) {
      // 添加复杂度
      this.addComplexity(
        complexity, 
        node, 
        ruleId, 
        description,
        this.getContextForRule(ruleId)
      );
    }
  }

  /**
   * 使用规则引擎评估节点复杂度
   * 优先使用 AsyncRuleEngine，如果不可用或失败则回退到 RuleRegistry
   * @param node AST 节点
   * @returns 评估结果
   */
  private evaluateNodeWithRules(node: Node): { complexity: number; ruleId: string; description: string } {
    // 如果有异步规则引擎，尝试使用它（但由于这是同步调用，我们需要特殊处理）
    if (this.asyncRuleEngine) {
      try {
        // 异步引擎可以处理，但我们在同步环境中，所以使用 RuleRegistry 作为回退
        console.debug('AsyncRuleEngine available but in sync context, using RuleRegistry fallback');
      } catch (asyncError) {
        console.debug('AsyncRuleEngine check failed, using RuleRegistry fallback');
      }
    }
    
    // 使用传统 RuleRegistry 进行评估
    const ruleId = this.getRuleIdForNodeType(node.type, node);
    const rule = RuleRegistry.getRule(ruleId);
    
    if (!rule || !rule.enabled) {
      return { complexity: 0, ruleId: 'unknown', description: 'Unknown rule' };
    }
    
    // 检查节点是否需要特殊处理
    if (this.shouldSkipNode(node)) {
      return { complexity: 0, ruleId, description: rule.description };
    }
    
    // 计算基础复杂度 + 嵌套增量
    const baseComplexity = rule.defaultIncrement;
    const nestingIncrement = this.getNestingIncrement();
    const totalComplexity = baseComplexity + nestingIncrement;
    
    // 对于逻辑运算符，还需要检查混用惩罚
    if (ruleId === 'logical-operator') {
      this.checkAndApplyMixingPenaltyOnce(node as any);
    }
    
    return { 
      complexity: totalComplexity, 
      ruleId, 
      description: rule.description 
    };
  }

  /**
   * 根据节点类型获取规则ID
   * @param nodeType 节点类型
   * @param node 可选的节点实例，用于更精确的判断
   * @returns 规则ID
   */
  private getRuleIdForNodeType(nodeType: string, node?: Node): string {
    const ruleMap: Record<string, string> = {
      'IfStatement': 'if-statement',
      'WhileStatement': 'while-statement',
      'DoWhileStatement': 'do-while-statement',
      'ForStatement': 'for-statement',
      'ForInStatement': 'for-statement',
      'ForOfStatement': 'for-statement',
      'SwitchStatement': 'switch-statement',
      'TryStatement': 'try-statement',
      'CatchClause': 'catch-clause',
      'ConditionalExpression': 'conditional-expression',
      'LogicalExpression': 'logical-operator',
      'CallExpression': 'recursive-call'
    };
    
    // 特殊处理 BinaryExpression：只有真正的逻辑运算符才映射到 logical-operator
    if (nodeType === 'BinaryExpression') {
      if (node && this.isLogicalOperator(node as any)) {
        return 'logical-operator';
      }
      // 非逻辑运算符的 BinaryExpression 不应该被处理
      return 'unknown-rule';
    }
    
    return ruleMap[nodeType] || 'unknown-rule';
  }

  /**
   * 检查是否应跳过节点的复杂度计算
   * @param node AST 节点
   * @returns 是否跳过
   */
  private shouldSkipNode(node: Node): boolean {
    // 逻辑运算符特殊处理
    if (node.type === 'BinaryExpression' || node.type === 'LogicalExpression') {
      const logicalNode = node as any;
      if (!this.isLogicalOperator(logicalNode)) {
        return true; // 不是逻辑运算符，跳过
      }
      if (this.isDefaultValueAssignment(logicalNode)) {
        return true; // 是默认值赋值，跳过
      }
    }
    
    // 递归调用特殊处理
    if (node.type === 'CallExpression') {
      return !this.isRecursiveCall(node as any);
    }
    
    return false;
  }

  /**
   * 获取规则的上下文信息
   * @param ruleId 规则ID
   * @returns 上下文信息
   */
  private getContextForRule(ruleId: string): string {
    const baseContext = `嵌套层级: ${this.nestingLevel}`;
    
    switch (ruleId) {
      case 'logical-operator':
        return baseContext;
      case 'recursive-call':
        return '递归函数调用';
      default:
        return `基础分(1) + 嵌套分(${this.getNestingIncrement()})`;
    }
  }

  /**
   * 验证并修正节点的 span 信息 - L1 层策略映射增强版本
   * 
   * 实现"结构性修正层"的核心逻辑，现已集成策略映射系统：
   * 1. 首先尝试使用节点特定的位置策略
   * 2. 如果策略失败，尝试回退策略
   * 3. 继续使用父节点的 span 信息进行回退
   * 4. 最后使用多级降级机制确保总有有效位置返回
   * 
   * @param node 要验证的 AST 节点
   * @returns 有效的字节偏移位置
   */
  private validateSpan(node: Node): number {
    try {
      // 第一步：尝试使用 L1 层策略映射系统
      const strategyResult = this.applyPositionStrategy(node);
      if (strategyResult !== null) {
        this.recordSpanValidation(node, strategyResult, 'strategy-mapping', true);
        return strategyResult;
      }

      // 第二步：检查节点自身的 span 有效性
      if (this.isValidSpan(node)) {
        const originalSpan = (node as NodeWithSpan).span.start;
        this.recordSpanValidation(node, originalSpan, 'original', true);
        return originalSpan;
      }

      // 第三步：尝试使用父节点的 span 信息进行结构化回退
      const correctedSpan = this.attemptParentSpanFallback(node);
      if (correctedSpan !== null) {
        this.recordSpanValidation(node, correctedSpan, 'parent-fallback', true);
        return correctedSpan;
      }

      // 第四步：使用多级降级机制，确保总有有效位置返回
      const defaultSpan = this.getDefaultSpan();
      this.recordSpanValidation(node, defaultSpan, 'default-fallback', true);
      return defaultSpan;

    } catch (error) {
      // span 修正失败，使用最保守的回退策略
      const fallbackSpan = 0;
      this.recordSpanValidation(node, fallbackSpan, 'error-fallback', false);
      this.recordSpanError(node, error as Error);
      return fallbackSpan;
    }
  }

  /**
   * 应用 L1 层位置策略系统
   * 使用策略映射表为节点找到最佳位置
   * @param node AST 节点
   * @returns 策略定位的位置，如果所有策略都失败则返回 null
   */
  private applyPositionStrategy(node: Node): number | null {
    const strategyEntry = ComplexityVisitor.getPositionStrategy(node.type);
    if (!strategyEntry) {
      // 没有为此节点类型注册策略，回退到传统逻辑
      return null;
    }

    try {
      // 尝试主要策略
      const primaryResult = strategyEntry.strategy(node);
      if (primaryResult !== null) {
        return primaryResult;
      }

      // 尝试回退策略
      if (strategyEntry.fallbackStrategy) {
        const fallbackResult = strategyEntry.fallbackStrategy(node);
        if (fallbackResult !== null) {
          return fallbackResult;
        }
      }

      // 尝试基于源代码的关键字查找（实例级回退）
      const instanceFallback = this.findKeywordPositionInSource(node);
      if (instanceFallback !== null) {
        return instanceFallback;
      }

    } catch (error) {
      // 策略执行失败，记录错误并回退
      if (this.detailCollector) {
        try {
          this.detailCollector.addErrorStep(
            {
              line: 0,
              column: 0,
              increment: 0,
              ruleId: 'strategy-error',
              description: `位置策略执行失败: ${node.type}`,
              context: error instanceof Error ? error.message : String(error)
            },
            `节点 ${node.type} 的位置策略执行失败: ${error instanceof Error ? error.message : String(error)}`
          );
        } catch (detailError) {
          console.warn(`Failed to record strategy error for ${node.type}:`, detailError);
        }
      }
    }

    return null;
  }

  /**
   * 在源代码中查找节点对应的关键字位置
   * 实例级的回退策略，可以访问源代码进行字符串搜索
   * @param node AST 节点
   * @returns 关键字位置，如果未找到则返回 null
   */
  private findKeywordPositionInSource(node: Node): number | null {
    // 根据节点类型确定要查找的关键字
    const keywordMap: Record<string, string> = {
      'IfStatement': 'if',
      'WhileStatement': 'while',
      'DoWhileStatement': 'do',
      'ForStatement': 'for',
      'ForInStatement': 'for',
      'ForOfStatement': 'for',
      'SwitchStatement': 'switch',
      'TryStatement': 'try',
      'CatchClause': 'catch',
      'ConditionalExpression': '?',
      'FunctionDeclaration': 'function',
      'ArrowFunctionExpression': '=>'
    };

    const keyword = keywordMap[node.type];
    if (!keyword) {
      return null;
    }

    // 基于父节点确定搜索范围
    const searchRange = this.getSearchRangeForNode(node);
    if (!searchRange) {
      // 如果无法确定搜索范围，在整个源代码中查找
      const index = this.sourceCode.indexOf(keyword);
      return index >= 0 ? index : null;
    }

    // 在指定范围内查找关键字
    const sourceSegment = this.sourceCode.slice(searchRange.start, searchRange.end);
    const relativeIndex = sourceSegment.indexOf(keyword);
    
    if (relativeIndex >= 0) {
      return searchRange.start + relativeIndex;
    }

    return null;
  }

  /**
   * 为节点确定关键字搜索范围
   * 基于父节点或节点自身的 span 信息来限制搜索范围
   * @param node AST 节点
   * @returns 搜索范围，如果无法确定则返回 null
   */
  private getSearchRangeForNode(node: Node): TokenSearchRange | null {
    // 尝试使用节点自身的 span
    const nodeWithSpan = node as any;
    if (nodeWithSpan.span && typeof nodeWithSpan.span.start === 'number' && typeof nodeWithSpan.span.end === 'number') {
      return {
        start: Math.max(0, nodeWithSpan.span.start - 50), // 向前搜索 50 个字符
        end: Math.min(this.sourceCode.length, nodeWithSpan.span.end + 10) // 向后搜索 10 个字符
      };
    }

    // 尝试使用父节点的 span
    const parent = this.getParent();
    if (parent) {
      const parentWithSpan = parent as any;
      if (parentWithSpan.span && typeof parentWithSpan.span.start === 'number' && typeof parentWithSpan.span.end === 'number') {
        return {
          start: parentWithSpan.span.start,
          end: parentWithSpan.span.end
        };
      }
    }

    return null;
  }

  /**
   * 记录 span 验证结果
   * @param node 被验证的节点
   * @param span 验证后的 span 位置
   * @param method 验证方法
   * @param success 是否成功
   */
  private recordSpanValidation(node: Node, span: number, method: string, success: boolean): void {
    if (this.detailCollector) {
      try {
        const position = PositionConverter.spanToPosition(this.sourceCode, span);
        const level = success ? (method === 'original' ? 'DEBUG' : 'INFO') : 'WARNING';
        
        this.detailCollector.addStepWithDiagnostic(
          {
            line: position.line,
            column: position.column,
            increment: 0, // span 验证不增加复杂度
            ruleId: 'span-validation',
            description: `Span验证: ${node.type}`,
            context: `方法: ${method}, 位置: ${position.line}:${position.column}, 状态: ${success ? '成功' : '失败'}`
          },
          level as any,
          `节点 ${node.type} 的 span 验证${success ? '成功' : '失败'}，使用 ${method} 方法，位置: ${position.line}:${position.column}`
        );
      } catch (error) {
        // 如果详细信息记录失败，不影响主要流程
        console.warn(`Failed to record span validation for ${node.type}:`, error);
      }
    }
  }

  /**
   * 检查 span 是否有效
   * @param node 要检查的节点
   * @returns 是否有效
   */
  private isValidSpan(node: Node): node is NodeWithSpan {
    const nodeWithSpan = node as NodeWithSpan;
    return nodeWithSpan.span && 
           typeof nodeWithSpan.span.start === 'number' && 
           typeof nodeWithSpan.span.end === 'number' &&
           nodeWithSpan.span.start >= 0 && 
           nodeWithSpan.span.end >= nodeWithSpan.span.start &&
           nodeWithSpan.span.start < this.sourceCode.length;
  }

  /**
   * 尝试使用父节点的 span 信息进行回退
   * 这是结构化修正的核心：基于 AST 层次结构而非文本猜测
   * @param node 当前节点
   * @returns 修正后的 span 位置，如果无法修正则返回 null
   */
  private attemptParentSpanFallback(node: Node): number | null {
    // 第一级：检查直接父节点
    const parent = this.getParent();
    if (parent && this.isValidSpan(parent)) {
      this.recordSpanFallbackStep(node, parent, 'direct-parent');
      return (parent as NodeWithSpan).span.start;
    }

    // 第二级：检查祖父节点
    const grandParent = this.getGrandParent();
    if (grandParent && this.isValidSpan(grandParent)) {
      this.recordSpanFallbackStep(node, grandParent, 'grandparent');
      return (grandParent as NodeWithSpan).span.start;
    }

    // 第三级：向上搜索整个祖先链
    const validAncestor = this.findValidAncestorSpan();
    if (validAncestor !== null) {
      this.recordSpanFallbackStep(node, null, 'ancestor-chain');
      return validAncestor;
    }

    // 第四级：尝试基于节点类型的智能推断
    const inferredSpan = this.inferSpanFromContext(node);
    if (inferredSpan !== null) {
      this.recordSpanFallbackStep(node, null, 'type-inference');
      return inferredSpan;
    }

    return null;
  }

  /**
   * 查找最近的具有有效 span 的祖先节点
   * @returns 有效的 span 位置，如果没有找到则返回 null
   */
  private findValidAncestorSpan(): number | null {
    const parentPath = this.getParentPath();
    
    // 从最近的祖先开始向上查找
    for (let i = parentPath.length - 1; i >= 0; i--) {
      const ancestor = parentPath[i];
      if (ancestor && this.isValidSpan(ancestor)) {
        return (ancestor as NodeWithSpan).span.start;
      }
    }

    return null;
  }

  /**
   * 基于节点类型和上下文推断 span 位置
   * 当所有父节点都无法提供有效 span 时的最后尝试
   * @param node 要推断的节点
   * @returns 推断的 span 位置，如果无法推断则返回 null
   */
  private inferSpanFromContext(node: Node): number | null {
    // 基于节点类型的启发式推断
    switch (node.type) {
      case 'IfStatement':
      case 'WhileStatement':
      case 'ForStatement':
      case 'SwitchStatement': {
        // 对于控制流语句，尝试查找相关的关键字位置
        return this.findControlFlowKeywordPosition(node.type);
      }
      
      case 'FunctionDeclaration':
      case 'MethodDefinition':
      case 'ArrowFunctionExpression': {
        // 对于函数相关节点，尝试找到 'function' 关键字或箭头
        return this.findFunctionKeywordPosition(node.type);
      }
      
      case 'BlockStatement': {
        // 对于块语句，尝试找到父级控制结构
        const controlParent = this.findNearestAncestorOfType('IfStatement') ||
                             this.findNearestAncestorOfType('WhileStatement') ||
                             this.findNearestAncestorOfType('ForStatement');
        if (controlParent && this.isValidSpan(controlParent)) {
          return (controlParent as NodeWithSpan).span.start;
        }
        break;
      }
      
      default:
        // 对于其他节点类型，暂不进行推断
        break;
    }

    return null;
  }

  /**
   * 查找控制流关键字的位置（使用智能Token查找系统）
   * @param nodeType 节点类型
   * @returns 关键字位置，如果未找到则返回 null
   */
  private findControlFlowKeywordPosition(nodeType: string): number | null {
    const keywordMap: Record<string, string> = {
      'IfStatement': 'if',
      'WhileStatement': 'while', 
      'DoWhileStatement': 'do',
      'ForStatement': 'for',
      'ForInStatement': 'for',
      'ForOfStatement': 'for',
      'SwitchStatement': 'switch',
      'TryStatement': 'try',
      'CatchClause': 'catch',
      'ConditionalExpression': '?'
    };

    const keyword = keywordMap[nodeType];
    if (!keyword) return null;

    // 使用Token查找系统进行精确定位
    // 由于这是在推断上下文中调用，我们需要创建一个临时节点或使用当前上下文
    const currentNode = this.getParent() || { type: nodeType, span: null };
    return this.findKeywordPosition(currentNode, keyword);
  }

  /**
   * 查找函数关键字的位置（使用智能Token查找系统）
   * @param nodeType 节点类型
   * @returns 关键字位置，如果未找到则返回 null
   */
  private findFunctionKeywordPosition(nodeType: string): number | null {
    // 创建临时节点来进行查找
    const currentNode = this.getParent() || { type: nodeType, span: null };
    
    // 根据函数类型查找相应的关键字
    if (nodeType === 'ArrowFunctionExpression') {
      return this.findKeywordPosition(currentNode, '=>');
    }
    
    return this.findKeywordPosition(currentNode, 'function');
  }

  /**
   * SWC Token 查找系统 - 精确定位关键字位置
   * 
   * 实现三级降级策略：
   * 1. SWC AST Token 查找（最精确）
   * 2. 字符串模式匹配（备用）
   * 3. indexOf 查找（兜底）
   * 
   * @param node AST 节点
   * @param keyword 要查找的关键字
   * @returns 关键字的字节偏移位置，如果未找到则返回 null
   */
  public findKeywordPosition(node: any, keyword: string): number | null {
    // 获取节点的搜索范围（如果有有效span则限制搜索范围）
    const searchRange = this.getSearchRange(node);
    
    try {
      // 策略1: SWC AST Token 查找
      const tokenPosition = this.findKeywordByTokenAnalysis(keyword, searchRange);
      if (tokenPosition !== null) {
        this.recordTokenSearchResult(node, keyword, tokenPosition, 'token-analysis');
        return tokenPosition;
      }

      // 策略2: 智能字符串模式匹配
      const patternPosition = this.findKeywordByPatternMatching(keyword, searchRange);
      if (patternPosition !== null) {
        this.recordTokenSearchResult(node, keyword, patternPosition, 'pattern-matching');
        return patternPosition;
      }

      // 策略3: 简单 indexOf 查找（兜底策略）
      const indexPosition = this.findKeywordByIndexOf(keyword, searchRange);
      if (indexPosition !== null) {
        this.recordTokenSearchResult(node, keyword, indexPosition, 'index-fallback');
        return indexPosition;
      }

      // 所有策略都失败
      this.recordTokenSearchResult(node, keyword, null, 'all-failed');
      return null;

    } catch (error) {
      this.recordTokenSearchError(node, keyword, error as Error);
      return null;
    }
  }

  /**
   * 获取节点的搜索范围
   * @param node AST 节点
   * @returns 搜索范围 { start, end }
   */
  private getSearchRange(node: any): { start: number; end: number } {
    // 如果节点有有效的 span，使用它来限制搜索范围
    if (this.isValidSpan(node)) {
      const span = (node as NodeWithSpan).span;
      // 扩展搜索范围以包含可能的前置空白和关键字
      const expandedStart = Math.max(0, span.start - 50);
      const expandedEnd = Math.min(this.sourceCode.length, span.end + 10);
      return { start: expandedStart, end: expandedEnd };
    }

    // 如果没有有效span，尝试使用父节点范围
    const parent = this.getParent();
    if (parent && this.isValidSpan(parent)) {
      const parentSpan = (parent as NodeWithSpan).span;
      return { start: parentSpan.start, end: parentSpan.end };
    }

    // 最后回退到整个源代码
    return { start: 0, end: this.sourceCode.length };
  }

  /**
   * 策略1: 使用 SWC AST 和 Token 分析查找关键字
   * @param keyword 关键字
   * @param range 搜索范围
   * @returns 关键字位置或 null
   */
  private findKeywordByTokenAnalysis(keyword: string, range: { start: number; end: number }): number | null {
    try {
      // 由于 @swc/core v1.13.2 没有直接的 tokenize API，
      // 我们使用 AST 结合智能搜索的方式来实现"Token查找"效果
      
      // 提取搜索范围内的代码片段
      const searchCode = this.sourceCode.slice(range.start, range.end);
      
      // 使用正则表达式进行词边界匹配（模拟Token级别的精确性）
      const keywordRegex = new RegExp(`\\b${this.escapeRegExp(keyword)}\\b`, 'g');
      let match;
      const candidates: number[] = [];
      
      while ((match = keywordRegex.exec(searchCode)) !== null) {
        const absolutePosition = range.start + match.index;
        candidates.push(absolutePosition);
      }
      
      // 如果找到候选位置，选择最合适的一个
      if (candidates.length > 0) {
        // 对于多个匹配，选择第一个（通常是节点开始处的关键字）
        return candidates[0];
      }
      
      return null;
    } catch (error) {
      console.debug('Token analysis failed:', error);
      return null;
    }
  }

  /**
   * 策略2: 智能字符串模式匹配
   * @param keyword 关键字
   * @param range 搜索范围
   * @returns 关键字位置或 null
   */
  private findKeywordByPatternMatching(keyword: string, range: { start: number; end: number }): number | null {
    try {
      const searchCode = this.sourceCode.slice(range.start, range.end);
      
      // 创建多种可能的匹配模式
      const patterns = [
        // 标准模式：关键字后跟空白或括号
        new RegExp(`\\b${this.escapeRegExp(keyword)}\\s*[\\(\\{\\s]`, 'g'),
        // 行首模式：行首的关键字
        new RegExp(`^\\s*${this.escapeRegExp(keyword)}\\b`, 'gm'),
        // 一般模式：词边界匹配
        new RegExp(`\\b${this.escapeRegExp(keyword)}\\b`, 'g')
      ];
      
      for (const pattern of patterns) {
        pattern.lastIndex = 0; // 重置正则表达式状态
        const match = pattern.exec(searchCode);
        if (match) {
          // 找到匹配的关键字本身的位置（不包括后续的空白或括号）
          const keywordStart = match.index;
          const actualKeywordMatch = match[0].match(new RegExp(`\\b${this.escapeRegExp(keyword)}\\b`));
          if (actualKeywordMatch) {
            const keywordOffset = match[0].indexOf(actualKeywordMatch[0]);
            return range.start + keywordStart + keywordOffset;
          }
        }
      }
      
      return null;
    } catch (error) {
      console.debug('Pattern matching failed:', error);
      return null;
    }
  }

  /**
   * 策略3: 简单 indexOf 查找（兜底策略）
   * @param keyword 关键字
   * @param range 搜索范围
   * @returns 关键字位置或 null
   */
  private findKeywordByIndexOf(keyword: string, range: { start: number; end: number }): number | null {
    try {
      const searchCode = this.sourceCode.slice(range.start, range.end);
      const index = searchCode.indexOf(keyword);
      
      if (index >= 0) {
        return range.start + index;
      }
      
      return null;
    } catch (error) {
      console.debug('indexOf search failed:', error);
      return null;
    }
  }

  /**
   * 转义正则表达式中的特殊字符
   * @param string 要转义的字符串
   * @returns 转义后的字符串
   */
  private escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * 记录Token搜索结果
   * @param node 节点
   * @param keyword 关键字
   * @param position 找到的位置
   * @param method 使用的方法
   */
  private recordTokenSearchResult(node: any, keyword: string, position: number | null, method: string): void {
    if (this.detailCollector) {
      try {
        const resultPosition = position !== null ? 
          PositionConverter.spanToPosition(this.sourceCode, position) : 
          { line: 0, column: 0 };
        
        this.detailCollector.addStepWithDiagnostic(
          {
            line: resultPosition.line,
            column: resultPosition.column,
            increment: 0,
            ruleId: 'token-search',
            description: `Token查找: ${keyword}`,
            context: `节点: ${node.type}, 方法: ${method}, 结果: ${position !== null ? '成功' : '失败'}`
          },
          position !== null ? 'DEBUG' as any : 'WARNING' as any,
          `使用 ${method} 方法${position !== null ? '成功找到' : '未找到'} '${keyword}' 关键字${position !== null ? ` (位置: ${position})` : ''}`
        );
      } catch (error) {
        console.warn(`Failed to record token search result for ${keyword}:`, error);
      }
    }
  }

  /**
   * 记录Token搜索错误
   * @param node 节点
   * @param keyword 关键字
   * @param error 错误信息
   */
  private recordTokenSearchError(node: any, keyword: string, error: Error): void {
    if (this.detailCollector) {
      try {
        this.detailCollector.addErrorStep(
          {
            line: 0,
            column: 0,
            increment: 0,
            ruleId: 'token-search-error',
            description: `Token查找失败: ${keyword}`,
            context: error.message
          },
          `Token查找系统在处理 '${keyword}' 时发生错误: ${error.message}`
        );
      } catch (detailError) {
        console.error(`Failed to record token search error for ${keyword}:`, detailError);
      }
    }
  }

  /**
   * 记录 span 回退步骤
   * @param node 被修正的节点
   * @param fallbackNode 提供回退 span 的节点
   * @param method 回退方法
   */
  private recordSpanFallbackStep(node: Node, fallbackNode: Node | null, method: string): void {
    if (this.detailCollector) {
      try {
        this.detailCollector.addStepWithDiagnostic(
          {
            line: 0, // 将在记录时通过 span 转换得到
            column: 0,
            increment: 0,
            ruleId: 'span-fallback',
            description: `Span回退: ${node.type}`,
            context: `方法: ${method}, 来源: ${fallbackNode?.type || 'unknown'}`
          },
          'INFO' as any,
          `节点 ${node.type} 使用 ${method} 方法进行 span 回退${fallbackNode ? `，基于 ${fallbackNode.type} 节点` : ''}`
        );
      } catch (error) {
        console.warn(`Failed to record span fallback for ${node.type}:`, error);
      }
    }
  }

  /**
   * 获取默认 span 位置
   * 当所有修正策略都失败时使用
   * @returns 默认的字节偏移位置
   */
  private getDefaultSpan(): number {
    // 多级降级策略，确保总有有效位置返回
    
    // 第一级：尝试找到当前函数的开始位置
    const functionStart = this.findCurrentFunctionStart();
    if (functionStart !== null) {
      return functionStart;
    }

    // 第二级：尝试找到文件中第一个有效的代码位置
    const firstCodePosition = this.findFirstCodePosition();
    if (firstCodePosition !== null) {
      return firstCodePosition;
    }

    // 第三级：返回文件开始位置（绝对保底）
    return 0;
  }

  /**
   * 查找当前函数的开始位置
   * @returns 函数开始位置，如果未找到则返回 null
   */
  private findCurrentFunctionStart(): number | null {
    // 向上查找函数声明节点
    const functionNode = this.findNearestAncestorOfType('FunctionDeclaration') ||
                        this.findNearestAncestorOfType('MethodDefinition') ||
                        this.findNearestAncestorOfType('ArrowFunctionExpression') ||
                        this.findNearestAncestorOfType('FunctionExpression');
    
    if (functionNode && this.isValidSpan(functionNode)) {
      return (functionNode as NodeWithSpan).span.start;
    }

    return null;
  }

  /**
   * 查找文件中第一个有效的代码位置
   * 跳过注释、空白行等
   * @returns 第一个代码位置，如果未找到则返回 null
   */
  private findFirstCodePosition(): number | null {
    const lines = this.sourceCode.split('\n');
    let offset = 0;

    for (const line of lines) {
      const trimmedLine = line.trim();
      
      // 跳过空行和注释行
      if (trimmedLine.length > 0 && 
          !trimmedLine.startsWith('//') && 
          !trimmedLine.startsWith('/*') &&
          !trimmedLine.startsWith('*')) {
        // 找到第一个非空白字符的位置
        const firstNonSpace = line.search(/\S/);
        return firstNonSpace >= 0 ? offset + firstNonSpace : offset;
      }
      
      offset += line.length + 1; // +1 for newline character
    }

    return null;
  }

  /**
   * 记录 span 修正错误
   * @param node 节点
   * @param error 错误信息
   */
  private recordSpanError(node: Node, error: Error): void {
    if (this.detailCollector) {
      try {
        this.detailCollector.addErrorStep(
          {
            line: 0,
            column: 0,
            increment: 0,
            ruleId: 'span-error',
            description: `Span修正失败: ${node.type}`,
            context: error.message
          },
          `无法修正节点 ${node.type} 的 span: ${error.message}`
        );
      } catch (detailError) {
        // 双重错误，只记录到控制台
        console.error(`Failed to record span error for ${node.type}:`, detailError);
      }
    }
  }

  /**
   * 处理节点访问过程中的错误
   * @param node 出错的节点
   * @param error 错误信息
   */
  private handleVisitError(node: Node, error: Error): void {
    console.warn(`Error visiting node ${node.type}:`, error.message);
    
    if (this.detailCollector) {
      try {
        this.detailCollector.addErrorStep(
          {
            line: 0,
            column: 0,
            increment: 0,
            ruleId: 'visit-error',
            description: `访问节点失败: ${node.type}`,
            context: error.message
          },
          `节点 ${node.type} 访问过程中发生错误: ${error.message}`
        );
      } catch (detailError) {
        console.error(`Failed to record visit error for ${node.type}:`, detailError);
      }
    }
  }

  /**
   * 增加复杂度
   * 内部辅助方法，用于在具体的节点访问方法中增加复杂度
   * @param increment 复杂度增量
   * @param node 相关节点
   * @param ruleId 规则标识符
   * @param description 规则描述
   * @param context 可选的上下文信息
   */
  protected addComplexity(
    increment: number, 
    node: Node, 
    ruleId: string, 
    description: string,
    context?: string
  ): void {
    this.totalComplexity += increment;

    if (this.detailCollector && increment > 0) {
      try {
        const validSpan = this.validateSpan(node);
        const position = PositionConverter.spanToPosition(this.sourceCode, validSpan);
        
        this.detailCollector.addStep({
          line: position.line,
          column: position.column,
          increment,
          ruleId,
          description,
          context: context || `嵌套层级: ${this.nestingLevel}`
        });
      } catch (error) {
        // 如果详细信息记录失败，复杂度仍然需要计算
        console.warn(`Failed to record complexity step for ${ruleId}:`, error);
      }
    }
  }

  /**
   * 进入嵌套上下文
   * 在访问嵌套结构（如 if、for 等）时调用
   */
  protected enterNesting(): void {
    this.nestingLevel++;
  }

  /**
   * 退出嵌套上下文
   * 在离开嵌套结构时调用
   */
  protected exitNesting(): void {
    this.nestingLevel = Math.max(0, this.nestingLevel - 1);
  }

  /**
   * 获取当前嵌套层级的复杂度增量
   * 根据嵌套深度计算额外的复杂度
   * @returns 嵌套复杂度增量
   */
  protected getNestingIncrement(): number {
    return this.nestingLevel;
  }

  // =============================================================================
  // 辅助方法：用于节点检查和混用逻辑处理
  // =============================================================================

  /**
   * 检测逻辑运算符
   * @param node 节点
   * @returns 是否为逻辑运算符
   */
  private isLogicalOperator(node: any): boolean {
    return node.operator === '&&' || node.operator === '||' || node.operator === '??';
  }

  /**
   * 检测是否为默认值赋值（需要排除）
   * @param node 节点
   * @returns 是否为默认值赋值
   */
  private isDefaultValueAssignment(node: any): boolean {
    // 检查 ?? 空值合并操作符 (总是应该被排除)
    if (node.operator === '??') {
      return true;
    }
    
    // 检查 || 用于默认值赋值的情况
    if (node.operator === '||') {
      return this.isInDefaultValueAssignmentContext(node);
    }
    
    // 检查 && 在某些默认值赋值模式中的使用
    if (node.operator === '&&') {
      return this.isPartOfDefaultValuePattern(node);
    }
    
    return false;
  }

  /**
   * 检测节点是否在默认值赋值上下文中
   * 简化版本：检查是否在赋值上下文中
   * @param node 节点
   * @returns 是否在赋值上下文中
   */
  private isInDefaultValueAssignmentContext(node: any): boolean {
    // 简化实现：检查父节点类型
    const parent = this.getParent();
    if (!parent) {
      return false;
    }
    
    // 检查是否在变量声明或赋值表达式中
    return parent.type === 'VariableDeclarator' || 
           parent.type === 'AssignmentExpression';
  }

  /**
   * 检测 && 运算符是否是默认值赋值模式的一部分
   * @param node 节点
   * @returns 是否是默认值模式的一部分
   */
  private isPartOfDefaultValuePattern(node: any): boolean {
    // 简化实现：检查是否在赋值上下文中
    return this.isInDefaultValueAssignmentContext(node);
  }

  /**
   * 检测递归调用
   * @param node 调用节点
   * @returns 是否为递归调用
   */
  private isRecursiveCall(node: any): boolean {
    // 获取调用的函数名
    const callee = this.getCalleeIdentifier(node);
    if (!callee || !this.currentFunctionName) {
      return false;
    }
    
    // 比较调用名与当前函数名
    return callee === this.currentFunctionName;
  }

  /**
   * 获取调用表达式的函数标识符
   * @param callNode 调用节点
   * @returns 函数标识符
   */
  private getCalleeIdentifier(callNode: any): string | null {
    const callee = callNode.callee;
    
    if (!callee) {
      return null;
    }
    
    // 直接函数调用: functionName()
    if (callee.type === 'Identifier') {
      return callee.value || callee.name;
    }
    
    // 成员表达式调用: obj.method() - 检查是否是this.method()
    if (callee.type === 'MemberExpression') {
      const object = callee.object;
      const property = callee.property;
      
      // this.functionName() 的情况
      if (object?.type === 'ThisExpression' && property?.type === 'Identifier') {
        return property.value || property.name;
      }
    }
    
    return null;
  }

  /**
   * 检查并应用混用惩罚（确保每个表达式树只应用一次）
   * @param node 当前逻辑运算符节点
   */
  private checkAndApplyMixingPenaltyOnce(node: any): void {
    // 查找逻辑表达式树的根节点
    const rootNode = this.findLogicalExpressionRoot(node);
    
    // 如果这个表达式树已经处理过混用惩罚，跳过
    if (this.processedMixingNodes.has(rootNode)) {
      return;
    }
    
    // 检查整个表达式树是否存在混用
    if (this.detectLogicalOperatorMixing(rootNode)) {
      this.addComplexity(
        1,
        rootNode,
        'logical-operator-mixing',
        '逻辑运算符混用惩罚',
        '混用不同类型的逻辑运算符'
      );
      
      // 标记这个表达式树已经处理过
      this.processedMixingNodes.add(rootNode);
    }
  }

  /**
   * 检测逻辑运算符混用
   * 完整版本实现，支持括号豁免检测
   * @param node 逻辑运算符节点
   * @returns 是否检测到混用
   */
  private detectLogicalOperatorMixing(node: any): boolean {
    // 检查配置是否启用混用检测
    if (!this.options.enableMixedLogicOperatorPenalty) {
      return false;
    }
    
    const currentOperator = (node as any).operator;
    if (!currentOperator || !['&&', '||'].includes(currentOperator)) {
      return false;
    }
    
    // 早期退出：检查是否为默认值赋值（需要豁免）
    if (this.isDefaultValueAssignment(node)) {
      return false;
    }

    // 检查括号豁免 - 优先检查，避免昂贵的运算符收集
    if (this.hasParenthesizedOperands(node)) {
      return false;
    }

    // 检查子节点是否包含不同的逻辑运算符
    const hasConflictingOperators = this.hasConflictingLogicalOperators(node, currentOperator);
    
    // 检查父级是否包含不同的逻辑运算符
    const parent = this.getParent();
    const parentHasConflict = parent && 
                             this.isLogicalExpressionType(parent) && 
                             (parent as any).operator !== currentOperator &&
                             ['&&', '||'].includes((parent as any).operator);
                             
    return hasConflictingOperators || Boolean(parentHasConflict);
  }

  /**
   * 检查逻辑表达式的操作数是否被括号包裹
   * 支持括号豁免机制
   * @param node 逻辑运算符节点
   * @returns 是否被括号包裹
   */
  private hasParenthesizedOperands(node: any): boolean {
    // 直接检查当前节点的左右操作数是否被括号包裹
    if (this.isDirectlyParenthesized(node.left) || this.isDirectlyParenthesized(node.right)) {
      return true;
    }
    
    // 检查是否存在逻辑分组的括号（消除歧义的括号）
    if (this.hasLogicalGroupingParentheses(node)) {
      return true;
    }
    
    return false;
  }

  /**
   * 检查节点是否直接被括号包裹
   * @param node 节点
   * @returns 是否被括号包裹
   */
  private isDirectlyParenthesized(node: any): boolean {
    return node && node.type === 'ParenthesizedExpression';
  }

  /**
   * 检查是否存在逻辑分组的括号
   * 例如: (a && b) || c 或 a && (b || c)
   * @param node 节点
   * @returns 是否存在逻辑分组括号
   */
  private hasLogicalGroupingParentheses(node: any): boolean {
    if (!node) return false;
    
    // 检查左子表达式
    if (node.left && this.containsLogicalGrouping(node.left)) {
      return true;
    }
    
    // 检查右子表达式  
    if (node.right && this.containsLogicalGrouping(node.right)) {
      return true;
    }
    
    return false;
  }

  /**
   * 递归检查表达式是否包含逻辑分组括号
   * @param node 节点
   * @returns 是否包含逻辑分组
   */
  private containsLogicalGrouping(node: any): boolean {
    if (!node || typeof node !== 'object') return false;
    
    // 如果是括号表达式且包含逻辑运算符，则认为是分组括号
    if (node.type === 'ParenthesizedExpression') {
      return this.containsLogicalOperators(node.expression);
    }
    
    // 递归检查子节点
    if (node.left && this.containsLogicalGrouping(node.left)) return true;
    if (node.right && this.containsLogicalGrouping(node.right)) return true;
    
    return false;
  }

  /**
   * 检查表达式是否包含逻辑运算符
   * @param node 节点
   * @returns 是否包含逻辑运算符
   */
  private containsLogicalOperators(node: any): boolean {
    if (!node || typeof node !== 'object') return false;
    
    // 检查当前节点是否是逻辑运算符
    if ((node.type === 'LogicalExpression' || node.type === 'BinaryExpression') && 
        ['&&', '||'].includes(node.operator)) {
      return true;
    }
    
    // 递归检查子节点
    const checkNode = (childNode: any): boolean => {
      if (childNode && typeof childNode === 'object') {
        return this.containsLogicalOperators(childNode);
      }
      return false;
    };
    
    // 检查所有子属性
    for (const key in node) {
      if (key === 'span' || key === 'type') continue;
      
      const value = node[key];
      if (Array.isArray(value)) {
        if (value.some(checkNode)) return true;
      } else if (checkNode(value)) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * 检查节点是否包含与当前运算符冲突的逻辑运算符
   * @param node 节点
   * @param currentOperator 当前运算符
   * @returns 是否有冲突
   */
  private hasConflictingLogicalOperators(node: any, currentOperator: string): boolean {
    if (!node) return false;
    
    // 检查左右子节点
    const checkChild = (child: any): boolean => {
      if (!child) return false;
      
      // 直接检查逻辑表达式
      if (this.isLogicalExpressionType(child)) {
        return child.operator !== currentOperator && ['&&', '||'].includes(child.operator);
      }
      
      // 处理括号表达式：深入检查括号内的表达式
      if (child.type === 'ParenthesisExpression' && child.expression) {
        return this.checkChildForLogicalOperator(child.expression, currentOperator);
      }
      
      // 递归检查子节点的子节点（最多一层）
      return this.hasConflictingLogicalOperators(child, currentOperator);
    };
    
    return checkChild(node.left) || checkChild(node.right);
  }

  /**
   * 检查子节点是否包含与当前操作符不同的逻辑操作符
   */
  private checkChildForLogicalOperator(child: any, currentOperator: string): boolean {
    if (!child) return false;
    
    if (this.isLogicalExpressionType(child)) {
      return child.operator !== currentOperator && ['&&', '||'].includes(child.operator);
    }
    
    // 递归检查更深层的节点
    if (child.left && this.checkChildForLogicalOperator(child.left, currentOperator)) {
      return true;
    }
    
    if (child.right && this.checkChildForLogicalOperator(child.right, currentOperator)) {
      return true;
    }
    
    return false;
  }

  /**
   * 检查是否为逻辑表达式类型
   * @param node 节点
   * @returns 是否为逻辑表达式
   */
  private isLogicalExpressionType(node: any): boolean {
    return node && (node.type === 'LogicalExpression' || node.type === 'BinaryExpression');
  }

  /**
   * 查找逻辑表达式树的根节点
   * 向上查找，直到找到不是逻辑运算符的父节点
   * @param node 当前节点
   * @returns 逻辑表达式树的根节点
   */
  private findLogicalExpressionRoot(node: any): any {
    let current = node;
    let parent = this.getParent();
    
    // 向上查找，直到父节点不是逻辑表达式
    while (parent && this.isLogicalExpressionType(parent) && ['&&', '||'].includes((parent as any).operator)) {
      current = parent;
      // 这里需要模拟向上移动，但由于我们没有轻松的方式获取更高层的父节点
      // 我们使用一个简化的策略：如果当前节点的父节点是逻辑运算符，
      // 我们假设父节点就是根（这个假设在大多数情况下是正确的）
      break;
    }
    
    return current;
  }
}
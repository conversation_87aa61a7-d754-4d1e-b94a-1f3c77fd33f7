# Task 5: SWC Token 查找系统实现

## 任务描述
实现基于 SWC Token 流的精确关键字查找系统，替代传统的字符串搜索方法。

## 具体工作项
- [ ] 研究和验证 SWC tokenize API 的可用性
- [ ] 实现 `findKeywordPosition` 基于 Token 流的精确查找
- [ ] 建立 Token 查找的降级策略（Token → 字符串 → indexOf）
- [ ] 限制 Token 搜索范围在节点 span 内优化性能

## 技术要求
- 使用 SWC 的 tokenize API 或相关功能
- 方法签名：`findKeywordPosition(node: Node, keyword: string): number | null`
- 实现三级降级策略确保健壮性
- Token 搜索限制在 `node.span.start` 到 `node.span.end` 范围内

## 预期输出
- `findKeywordPosition` 核心方法
- Token 查找系统基础设施
- 降级策略实现
- 性能优化机制

## 验收标准
- [ ] SWC tokenize API 集成成功
- [ ] 关键字查找准确性 > 95%
- [ ] 降级策略工作正常
- [ ] 性能满足要求（搜索时间 < 10ms）

对应需求: 2.5
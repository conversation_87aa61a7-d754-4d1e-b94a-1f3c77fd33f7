# Task 6: 箭头函数精确定位实现

## 任务描述
实现箭头函数的精确定位功能，解决当前系统在处理箭头函数时定位不准确的核心痛点。

## 具体工作项
- [ ] 实现 `findArrowFunctionPosition` 专门处理箭头函数
- [ ] 支持不同形式的箭头函数（参数列表、单参数、箭头位置）
- [ ] 处理复杂箭头函数的嵌套和类型注解情况
- [ ] 集成到策略映射表中

## 技术要求
- 方法签名：`findArrowFunctionPosition(node: Node): number | null`
- 支持多种箭头函数形式：`() => {}`, `x => x`, `(a: string) => void`
- 处理嵌套箭头函数和复杂类型注解
- 集成任务5的 Token 查找系统

## 预期输出
- `findArrowFunctionPosition` 专门方法
- 多种箭头函数形式的支持
- 复杂情况的处理逻辑
- 策略映射表集成

## 验收标准
- [ ] 各种箭头函数形式定位准确
- [ ] 复杂嵌套情况处理正确
- [ ] 集成到策略系统正常工作
- [ ] 定位精确度 > 95%

对应需求: 2.2, 3.2
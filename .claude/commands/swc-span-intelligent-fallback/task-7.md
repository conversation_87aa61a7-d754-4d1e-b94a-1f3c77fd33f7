# Task 7: JSX 元素精确定位实现

## 任务描述
实现 JSX 元素的精确定位功能，区分 JSX 标签和表达式内容，解决前端生态中的核心定位问题。

## 具体工作项
- [ ] 实现 `findJsxOpeningTagPosition` 定位 JSX 开放标签
- [ ] 实现 `findJSXExpressionContentPosition` 定位表达式内容
- [ ] 处理 JSX 嵌套和复杂表达式的情况
- [ ] 区分 JSX 标签和表达式内容的定位策略

## 技术要求
- 方法签名：`findJsxOpeningTagPosition(node: Node): number | null`
- 方法签名：`findJSXExpressionContentPosition(node: Node): number | null`
- 支持嵌套 JSX 结构
- 准确区分标签名和表达式内容

## 预期输出
- `findJsxOpeningTagPosition` 方法
- `findJSXExpressionContentPosition` 方法
- JSX 嵌套处理逻辑
- 标签与表达式区分策略

## 验收标准
- [ ] JSX 标签定位准确
- [ ] 表达式内容定位正确
- [ ] 嵌套结构处理正常
- [ ] 复杂 JSX 场景支持良好

对应需求: 2.3, 3.1, 3.2
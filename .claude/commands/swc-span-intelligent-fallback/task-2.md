# Task 2: L0 层智能位置转换增强

## 任务描述
实现 `spanToPositionWithSmartFallback` 核心方法，集成语义分析能力到位置转换流程。

## 具体工作项
- [ ] 实现 `spanToPositionWithSmartFallback` 核心方法
- [ ] 集成语义分析能力到位置转换流程
- [ ] 优化缓存键生成策略，支持文件路径参数
- [ ] 添加边界情况的智能处理逻辑

## 技术要求
- 方法签名: `spanToPositionWithSmartFallback(sourceCode: string, spanStart: number, filePath: string): Position`
- 集成任务1实现的语义分析方法
- 智能处理 span 超出文件边界的情况
- 使用文件路径优化缓存性能

## 预期输出
- `spanToPositionWithSmartFallback` 核心 API 方法
- 增强的边界处理逻辑
- 优化的缓存策略

## 验收标准
- [ ] 方法正确处理各种边界情况
- [ ] 语义分析集成正常工作
- [ ] 缓存性能优化生效
- [ ] 通过边界测试用例

对应需求: 1.1, 1.3
# Task 3: L1 层策略映射表框架搭建

## 任务描述
在 `ComplexityVisitor` 中建立节点类型策略映射系统的基础框架。

## 具体工作项
- [ ] 设计 `NODE_POSITION_STRATEGIES` 映射表结构
- [ ] 实现策略函数类型定义和接口
- [ ] 迁移 2-3 个简单节点策略（如 IfStatement）
- [ ] 建立策略回退机制的基础框架

## 技术要求
- 使用 Map 结构：`Map<NodeType, PositionStrategy>`
- 策略函数类型：`(node: Node) => number | null`
- 支持策略优先级和回退机制
- 保持现有 API 兼容性

## 预期输出
- `NODE_POSITION_STRATEGIES` 映射表常量
- `PositionStrategy` 类型定义
- 基础策略函数实现（IfStatement, WhileStatement 等）
- 策略回退机制框架

## 验收标准
- [ ] 映射表结构设计合理
- [ ] 基础策略函数工作正常
- [ ] 回退机制框架完整
- [ ] 不影响现有功能

对应需求: 2.1, 2.2, 2.4
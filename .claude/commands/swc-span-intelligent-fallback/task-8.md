# Task 8: 策略映射表完善

## 任务描述
将任务6、7实现的箭头函数和 JSX 策略集成到策略映射表中，完善整个策略系统。

## 具体工作项
- [ ] 将箭头函数和 JSX 策略集成到映射表
- [ ] 实现控制流语句的关键字定位策略
- [ ] 添加函数表达式和类型定义的定位策略
- [ ] 建立策略优先级和回退机制

## 技术要求
- 更新 `NODE_POSITION_STRATEGIES` 映射表
- 为每种节点类型配置合适的策略函数
- 实现策略优先级排序
- 建立完整的回退链

## 预期输出
- 完整的策略映射表
- 所有主要节点类型的策略支持
- 策略优先级系统
- 健壮的回退机制

## 验收标准
- [ ] 映射表包含所有目标节点类型
- [ ] 策略函数工作正常
- [ ] 优先级和回退机制生效
- [ ] 系统整体稳定性良好

对应需求: 2.1, 2.2, 2.3, 2.4
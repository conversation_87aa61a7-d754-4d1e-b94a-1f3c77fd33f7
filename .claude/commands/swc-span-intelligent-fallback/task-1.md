# Task 1: L0 层基础语义分析方法实现

## 任务描述
在 `PositionConverter` 中实现基础语义分析方法，为智能位置转换提供语义感知能力。

## 具体工作项
- [ ] 在 `PositionConverter` 中实现 `isInsignificantLine` 方法
- [ ] 实现 `findNearestMeaningfulLine` 方法支持双向搜索  
- [ ] 实现 `findLastMeaningfulLine` 方法处理边界情况
- [ ] 添加文件级行分析缓存 `lineAnalysisCache`

## 技术要求
- 方法必须为静态方法 (static)
- 支持空行、注释行、仅有括号/分号等无意义行的识别
- 缓存系统需要支持文件路径作为键
- 双向搜索需要支持 'up' 和 'down' 方向参数

## 预期输出
- `isInsignificantLine(lineContent: string): boolean` 方法
- `findNearestMeaningfulLine(sourceCode: string, startLine: number, direction: 'up' | 'down'): Position` 方法  
- `findLastMeaningfulLine(sourceCode: string): Position` 方法
- `lineAnalysisCache: Map<string, Map<number, boolean>>` 缓存系统

## 验收标准
- [ ] 所有方法通过单元测试
- [ ] 准确识别各种无意义行类型
- [ ] 缓存系统正常工作
- [ ] 性能符合要求（不超过原实现 120%）

对应需求: 1.1, 1.2, 1.3, 1.4